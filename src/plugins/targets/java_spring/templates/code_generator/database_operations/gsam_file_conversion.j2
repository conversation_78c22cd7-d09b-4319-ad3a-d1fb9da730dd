**FILE PROCESSING OPERATION CONVERSION GUIDE**

Convert the following COBOL file processing operations to modern Spring Boot file I/O implementations:

**COBOL FILE OPERATIONS DETECTED:**
{% if gsam_operations %}
{% for operation in gsam_operations %}
- **{{ operation.type }}**: {{ operation.file_name }} → {{ operation.description }}
  - COBOL: `{{ operation.cobol_code }}`
  - File Control: {{ operation.pcb_name | default('None') }}
  - Data Buffer: {{ operation.io_area | default('None') }}
{% endfor %}
{% endif %}

**CONVERSION PATTERNS:**

**1. File Access Operations:**
```java
// COBOL: Open file for data processing operations
// Java: Convert to Spring Boot file I/O with proper resource management

@Service
public class {{ service_name }} {
    
    @Value("${file.processing.input-directory:./input}")
    private String inputDirectory;
    
    @Value("${file.processing.output-directory:./output}")
    private String outputDirectory;
    
    @Value("${file.processing.archive-directory:./archive}")
    private String archiveDirectory;
    
    @Value("${file.processing.error-directory:./error}")
    private String errorDirectory;
    
    private static final Logger logger = LoggerFactory.getLogger({{ service_name }}.class);
    
    /**
     * Open file for reading (equivalent to GSAM GU/GN operations)
     * COBOL: CALL 'CBLTDLI' USING 'GU', pcb, io-area
     */
    public BufferedReader openFileForReading(String fileName) throws FileOperationException {
        try {
            Path filePath = Paths.get(inputDirectory, fileName);
            
            if (!Files.exists(filePath)) {
                throw new FileNotFoundException("Input file not found: " + filePath);
            }
            
            if (!Files.isReadable(filePath)) {
                throw new FileAccessException("Input file is not readable: " + filePath);
            }
            
            BufferedReader reader = Files.newBufferedReader(filePath, StandardCharsets.UTF_8);
            logger.info("Successfully opened file for reading: {}", filePath);
            return reader;
            
        } catch (IOException e) {
            logger.error("Error opening file for reading: {}", fileName, e);
            throw new FileOperationException("Failed to open file for reading: " + fileName, e);
        }
    }
    
    /**
     * Open file for writing (equivalent to GSAM ISRT operations)
     * COBOL: CALL 'CBLTDLI' USING 'ISRT', pcb, io-area
     */
    public BufferedWriter openFileForWriting(String fileName) throws FileOperationException {
        try {
            Path filePath = Paths.get(outputDirectory, fileName);
            
            // Create output directory if it doesn't exist
            Files.createDirectories(filePath.getParent());
            
            BufferedWriter writer = Files.newBufferedWriter(filePath, StandardCharsets.UTF_8, 
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            logger.info("Successfully opened file for writing: {}", filePath);
            return writer;
            
        } catch (IOException e) {
            logger.error("Error opening file for writing: {}", fileName, e);
            throw new FileOperationException("Failed to open file for writing: " + fileName, e);
        }
    }
    
    /**
     * Open file for appending
     */
    public BufferedWriter openFileForAppending(String fileName) throws FileOperationException {
        try {
            Path filePath = Paths.get(outputDirectory, fileName);
            Files.createDirectories(filePath.getParent());
            
            BufferedWriter writer = Files.newBufferedWriter(filePath, StandardCharsets.UTF_8, 
                StandardOpenOption.CREATE, StandardOpenOption.APPEND);
            logger.info("Successfully opened file for appending: {}", filePath);
            return writer;
            
        } catch (IOException e) {
            logger.error("Error opening file for appending: {}", fileName, e);
            throw new FileOperationException("Failed to open file for appending: " + fileName, e);
        }
    }
}
```

**2. GSAM File Read Operations:**
```java
// COBOL: CALL 'CBLTDLI' USING 'GN', pcb, io-area (Get Next)
// Java: Convert to BufferedReader.readLine() with proper error handling

@Service
public class {{ service_name }} {
    
    /**
     * Read next record from file (equivalent to GSAM GN)
     * Returns null when end of file is reached
     */
    public {{ record_class_name }} readNextRecord(BufferedReader reader) throws FileOperationException {
        try {
            String line = reader.readLine();
            if (line == null) {
                // End of file reached - equivalent to GSAM status 'GB'
                logger.debug("End of file reached");
                return null;
            }
            
            // Parse the line using the data class parseFromString method
            {{ record_class_name }} record = {{ record_class_name }}.parseFromString(line);
            logger.debug("Successfully read record: {}", record);
            return record;
            
        } catch (IOException e) {
            logger.error("Error reading next record from file", e);
            throw new FileOperationException("Failed to read next record", e);
        } catch (Exception e) {
            logger.error("Error parsing record from line", e);
            throw new FileParsingException("Failed to parse record from file", e);
        }
    }
    
    /**
     * Read all records from file with batch processing
     */
    public List<{{ record_class_name }}> readAllRecords(String fileName, int batchSize) throws FileOperationException {
        List<{{ record_class_name }}> allRecords = new ArrayList<>();
        
        try (BufferedReader reader = openFileForReading(fileName)) {
            {{ record_class_name }} record;
            int recordCount = 0;
            
            while ((record = readNextRecord(reader)) != null) {
                allRecords.add(record);
                recordCount++;
                
                // Log progress for large files
                if (recordCount % batchSize == 0) {
                    logger.info("Processed {} records from file: {}", recordCount, fileName);
                }
            }
            
            logger.info("Successfully read {} total records from file: {}", recordCount, fileName);
            return allRecords;
            
        } catch (Exception e) {
            logger.error("Error reading all records from file: {}", fileName, e);
            throw new FileOperationException("Failed to read all records from file: " + fileName, e);
        }
    }
    
    /**
     * Process file records with streaming (for large files)
     */
    public void processFileRecords(String fileName, Consumer<{{ record_class_name }}> recordProcessor) throws FileOperationException {
        try (BufferedReader reader = openFileForReading(fileName)) {
            {{ record_class_name }} record;
            int recordCount = 0;
            
            while ((record = readNextRecord(reader)) != null) {
                try {
                    recordProcessor.accept(record);
                    recordCount++;
                    
                    if (recordCount % 1000 == 0) {
                        logger.info("Processed {} records from file: {}", recordCount, fileName);
                    }
                } catch (Exception e) {
                    logger.error("Error processing record {}: {}", recordCount + 1, record, e);
                    // Continue processing or rethrow based on business requirements
                    throw new RecordProcessingException("Failed to process record " + (recordCount + 1), e);
                }
            }
            
            logger.info("Successfully processed {} total records from file: {}", recordCount, fileName);
            
        } catch (Exception e) {
            logger.error("Error processing file records: {}", fileName, e);
            throw new FileOperationException("Failed to process file records: " + fileName, e);
        }
    }
}
```

**3. GSAM File Write Operations:**
```java
// COBOL: CALL 'CBLTDLI' USING 'ISRT', pcb, io-area (Insert)
// Java: Convert to BufferedWriter.write() with proper formatting

@Service
public class {{ service_name }} {
    
    /**
     * Write record to file (equivalent to GSAM ISRT)
     */
    public void writeRecord(BufferedWriter writer, {{ record_class_name }} record) throws FileOperationException {
        try {
            validateRequired(record, "record");
            
            // Convert record to string format (using toString or custom format method)
            String recordLine = record.toFixedWidthString();
            writer.write(recordLine);
            writer.newLine();
            
            logger.debug("Successfully wrote record: {}", record);
            
        } catch (IOException e) {
            logger.error("Error writing record to file: {}", record, e);
            throw new FileOperationException("Failed to write record to file", e);
        }
    }
    
    /**
     * Write multiple records to file with batch processing
     */
    public void writeAllRecords(String fileName, List<{{ record_class_name }}> records) throws FileOperationException {
        try (BufferedWriter writer = openFileForWriting(fileName)) {
            int recordCount = 0;
            
            for ({{ record_class_name }} record : records) {
                writeRecord(writer, record);
                recordCount++;
                
                // Flush periodically for large files
                if (recordCount % 1000 == 0) {
                    writer.flush();
                    logger.info("Written {} records to file: {}", recordCount, fileName);
                }
            }
            
            writer.flush();
            logger.info("Successfully wrote {} total records to file: {}", recordCount, fileName);
            
        } catch (Exception e) {
            logger.error("Error writing all records to file: {}", fileName, e);
            throw new FileOperationException("Failed to write all records to file: " + fileName, e);
        }
    }
    
    /**
     * Append records to existing file
     */
    public void appendRecords(String fileName, List<{{ record_class_name }}> records) throws FileOperationException {
        try (BufferedWriter writer = openFileForAppending(fileName)) {
            int recordCount = 0;
            
            for ({{ record_class_name }} record : records) {
                writeRecord(writer, record);
                recordCount++;
            }
            
            writer.flush();
            logger.info("Successfully appended {} records to file: {}", recordCount, fileName);
            
        } catch (Exception e) {
            logger.error("Error appending records to file: {}", fileName, e);
            throw new FileOperationException("Failed to append records to file: " + fileName, e);
        }
    }
}
```

**4. File Status Checking and Error Handling:**
```java
// COBOL: Check PCB status bytes for file operation results
// Java: Use try-catch with specific exceptions and status checking

@Service
public class {{ service_name }} {
    
    /**
     * Check file existence and accessibility (equivalent to PCB status checking)
     */
    public FileStatus checkFileStatus(String fileName, FileOperation operation) {
        try {
            Path filePath = Paths.get(getDirectoryForOperation(operation), fileName);
            
            if (!Files.exists(filePath)) {
                return FileStatus.FILE_NOT_FOUND; // Equivalent to GSAM status 'GE'
            }
            
            switch (operation) {
                case READ:
                    if (!Files.isReadable(filePath)) {
                        return FileStatus.ACCESS_DENIED;
                    }
                    break;
                case write:
                    if (Files.exists(filePath) && !Files.isWritable(filePath)) {
                        return FileStatus.ACCESS_DENIED;
                    }
                    break;
            }
            
            return FileStatus.SUCCESS; // Equivalent to GSAM status spaces
            
        } catch (Exception e) {
            logger.error("Error checking file status: {}", fileName, e);
            return FileStatus.SYSTEM_ERROR;
        }
    }
    
    /**
     * Handle file operation with comprehensive error checking
     */
    public <T> T executeFileOperation(String fileName, FileOperation operation, 
                                     FileOperationCallback<T> callback) throws FileOperationException {
        try {
            // Pre-operation validation
            FileStatus status = checkFileStatus(fileName, operation);
            if (status != FileStatus.SUCCESS) {
                throw new FileOperationException("File operation failed with status: " + status);
            }
            
            // Execute the operation
            T result = callback.execute();
            
            logger.info("File operation {} completed successfully for file: {}", operation, fileName);
            return result;
            
        } catch (FileOperationException e) {
            throw e; // Re-throw file operation exceptions
        } catch (Exception e) {
            logger.error("Unexpected error during file operation {} for file: {}", operation, fileName, e);
            throw new FileOperationException("Unexpected error during file operation", e);
        }
    }
    
    private String getDirectoryForOperation(FileOperation operation) {
        switch (operation) {
            case READ: return inputDirectory;
            case write: return outputDirectory;
            default: return inputDirectory;
        }
    }
}

// Enums and interfaces for file operations
public enum FileStatus {
    SUCCESS,           // Equivalent to GSAM status spaces
    FILE_NOT_FOUND,    // Equivalent to GSAM status 'GE'
    END_OF_FILE,       // Equivalent to GSAM status 'GB'
    ACCESS_DENIED,     // File permission issues
    SYSTEM_ERROR       // Other system errors
}

public enum FileOperation {
    READ, write, append
}

@FunctionalInterface
public interface FileOperationCallback<T> {
    T execute() throws Exception;
}
```

**5. File Archive and Error Handling:**
```java
// COBOL: File processing with error handling and archiving
// Java: Implement comprehensive file lifecycle management

@Service
public class {{ service_name }} {
    
    /**
     * Process file with automatic archiving and error handling
     */
    @Transactional
    public FileProcessingResult processFile(String fileName, FileProcessor processor) {
        FileProcessingResult result = new FileProcessingResult();
        result.setFileName(fileName);
        result.setStartTime(LocalDateTime.now());
        
        try {
            // Validate file exists and is readable
            FileStatus status = checkFileStatus(fileName, FileOperation.read);
            if (status != FileStatus.SUCCESS) {
                throw new FileOperationException("File validation failed: " + status);
            }
            
            // Process the file
            result = processor.processFile(fileName);
            
            // Archive successfully processed file
            archiveFile(fileName);
            result.setStatus(ProcessingStatus.SUCCESS);
            
            logger.info("Successfully processed file: {} with {} records", fileName, result.getRecordCount());
            
        } catch (Exception e) {
            logger.error("Error processing file: {}", fileName, e);
            
            // Move file to error directory
            moveFileToErrorDirectory(fileName, e);
            result.setStatus(ProcessingStatus.ERROR);
            result.setErrorMessage(e.getMessage());
        } finally {
            result.setEndTime(LocalDateTime.now());
            result.setProcessingDuration(Duration.between(result.getStartTime(), result.getEndTime()));
        }
        
        return result;
    }
    
    private void archiveFile(String fileName) {
        try {
            Path sourcePath = Paths.get(inputDirectory, fileName);
            Path archivePath = Paths.get(archiveDirectory, fileName + "." + 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            
            Files.createDirectories(archivePath.getParent());
            Files.move(sourcePath, archivePath);
            
            logger.info("File archived: {} -> {}", sourcePath, archivePath);
            
        } catch (IOException e) {
            logger.error("Error archiving file: {}", fileName, e);
            // Don't throw exception - archiving failure shouldn't fail the main process
        }
    }
    
    private void moveFileToErrorDirectory(String fileName, Exception error) {
        try {
            Path sourcePath = Paths.get(inputDirectory, fileName);
            Path errorPath = Paths.get(errorDirectory, fileName + ".error." + 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            
            Files.createDirectories(errorPath.getParent());
            Files.move(sourcePath, errorPath);
            
            // Create error log file
            Path errorLogPath = Paths.get(errorDirectory, fileName + ".error.log");
            Files.write(errorLogPath, error.toString().getBytes(), StandardOpenOption.CREATE, StandardOpenOption.APPEND);
            
            logger.info("File moved to error directory: {} -> {}", sourcePath, errorPath);
            
        } catch (IOException e) {
            logger.error("Error moving file to error directory: {}", fileName, e);
        }
    }
}

// Supporting classes
public class FileProcessingResult {
    private String fileName;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Duration processingDuration;
    private ProcessingStatus status;
    private int recordCount;
    private String errorMessage;
    
    // Getters and setters...
}

public enum ProcessingStatus {
    SUCCESS, ERROR, PARTIAL
}

@FunctionalInterface
public interface FileProcessor {
    FileProcessingResult processFile(String fileName) throws FileOperationException;
}
```

**REQUIRED IMPORTS:**
```java
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.Duration;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
```

**CONFIGURATION PROPERTIES:**
```properties
# File processing configuration
file.processing.input-directory=./input
file.processing.output-directory=./output
file.processing.archive-directory=./archive
file.processing.error-directory=./error
file.processing.batch-size=1000
file.processing.max-file-size=100MB
```
