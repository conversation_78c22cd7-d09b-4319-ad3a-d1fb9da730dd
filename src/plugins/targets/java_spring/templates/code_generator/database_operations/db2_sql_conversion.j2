**DATABASE QUERY OPERATION CONVERSION GUIDE**

Convert the following COBOL database query operations to modern Spring Data JPA implementations:

**COBOL DATABASE QUERY OPERATIONS DETECTED:**
{% if db2_operations %}
{% for operation in db2_operations %}
- **{{ operation.type }}**: {{ operation.table }} → {{ operation.description }}
  - COBOL: `{{ operation.cobol_code }}`
  - Input Parameters: {{ operation.host_variables | join(', ') if operation.host_variables else 'None' }}
  - Transaction Isolation: {{ operation.isolation | default('None') }}
{% endfor %}
{% endif %}

**CONVERSION PATTERNS:**

**1. Data Selection Operations:**
```java
// COBOL: Retrieve specific data columns based on search criteria
// Java: Convert to JPA @Query or repository method

@Repository
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {
    
    // Single record selection
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }}")
    Optional<{{ entity_name }}> findBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }});
    
    // Multiple column projection
    @Query("SELECT new {{ package_name }}.dto.{{ entity_name }}Dto(e.{{ field1 }}, e.{{ field2 }}) " +
           "FROM {{ entity_name }} e WHERE e.{{ condition_field }} = :{{ condition_param }}")
    Optional<{{ entity_name }}Dto> findProjectionBy{{ condition_field | title }}(@Param("{{ condition_param }}") {{ condition_type }} {{ condition_param }});
    
    // Multiple records with conditions
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} BETWEEN :startValue AND :endValue ORDER BY e.{{ sort_field }}")
    List<{{ entity_name }}> findByRange(@Param("startValue") {{ param_type }} startValue, 
                                       @Param("endValue") {{ param_type }} endValue);
    
    // Native SQL for complex queries (when JPA is insufficient)
    @Query(value = "SELECT * FROM {{ table_name }} WHERE {{ native_condition }} FOR UPDATE", nativeQuery = true)
    List<{{ entity_name }}> findWithNativeQuery(@Param("param1") {{ param_type }} param1);
}

// Service implementation
@Service
@Transactional(readOnly = true)
public class {{ service_name }} {
    
    @Autowired
    private {{ entity_name }}Repository repository;
    
    public {{ entity_name }} select{{ entity_name }}By{{ field_name | title }}({{ param_type }} {{ param_name }}) {
        try {
            return repository.findBy{{ field_name | title }}({{ param_name }})
                .orElseThrow(() -> new EntityNotFoundException(
                    "{{ entity_name }} not found with {{ field_name }}: " + {{ param_name }}));
        } catch (DataAccessException e) {
            logger.error("Database error retrieving {{ entity_name }} with {{ field_name }}: {}", {{ param_name }}, e);
            throw new DatabaseOperationException("Failed to retrieve {{ entity_name }}", e);
        }
    }
}
```

**2. EXEC SQL INSERT Operations:**
```java
// COBOL: EXEC SQL INSERT INTO table (col1, col2) VALUES (:host-var1, :host-var2) END-EXEC
// Java: Convert to JPA save operation

@Service
@Transactional
public class {{ service_name }} {
    
    @Autowired
    private {{ entity_name }}Repository repository;
    
    public {{ entity_name }} insert{{ entity_name }}({{ entity_name }} entity) {
        try {
            validateRequired(entity, "{{ entity_name | lower }}");
            
            // Pre-insert business validation
            validateInsertBusinessRules(entity);
            
            // Set audit fields if applicable
            entity.setCreatedDate(LocalDateTime.now());
            entity.setCreatedBy(getCurrentUser());
            
            {{ entity_name }} savedEntity = repository.save(entity);
            logger.info("Successfully inserted {{ entity_name }} with ID: {}", savedEntity.getId());
            return savedEntity;
            
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation inserting {{ entity_name }}: {}", entity, e);
            if (e.getCause() instanceof ConstraintViolationException) {
                throw new DatabaseConstraintException("Constraint violation: " + e.getMessage(), e);
            }
            throw new DatabaseOperationException("Failed to insert {{ entity_name }}", e);
        } catch (DataAccessException e) {
            logger.error("SQL error inserting {{ entity_name }}: {}", entity, e);
            throw new DatabaseOperationException("Failed to insert {{ entity_name }}", e);
        }
    }
    
    // Batch insert for performance
    @Transactional
    public List<{{ entity_name }}> insertBatch{{ entity_name }}(List<{{ entity_name }}> entities) {
        try {
            validateRequired(entities, "entities list");
            
            // Validate each entity
            entities.forEach(this::validateInsertBusinessRules);
            
            // Set audit fields
            LocalDateTime now = LocalDateTime.now();
            String currentUser = getCurrentUser();
            entities.forEach(entity -> {
                entity.setCreatedDate(now);
                entity.setCreatedBy(currentUser);
            });
            
            List<{{ entity_name }}> savedEntities = repository.saveAll(entities);
            logger.info("Successfully inserted {} {{ entity_name }} records", savedEntities.size());
            return savedEntities;
            
        } catch (DataAccessException e) {
            logger.error("SQL error batch inserting {{ entity_name }} records", e);
            throw new DatabaseOperationException("Failed to batch insert {{ entity_name }} records", e);
        }
    }
}
```

**3. EXEC SQL UPDATE Operations:**
```java
// COBOL: EXEC SQL UPDATE table SET col1 = :host-var1 WHERE condition END-EXEC
// Java: Convert to JPA save or @Modifying query

@Repository
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {
    
    // Bulk update using @Modifying
    @Modifying
    @Query("UPDATE {{ entity_name }} e SET e.{{ field_name }} = :{{ new_value }}, e.lastModifiedDate = :modifiedDate " +
           "WHERE e.{{ condition_field }} = :{{ condition_value }}")
    int updateBy{{ condition_field | title }}(@Param("{{ new_value }}") {{ field_type }} {{ new_value }},
                                            @Param("modifiedDate") LocalDateTime modifiedDate,
                                            @Param("{{ condition_value }}") {{ condition_type }} {{ condition_value }});
    
    // Conditional update with version checking
    @Modifying
    @Query("UPDATE {{ entity_name }} e SET e.{{ field_name }} = :{{ new_value }}, e.version = e.version + 1, " +
           "e.lastModifiedDate = :modifiedDate WHERE e.id = :id AND e.version = :version")
    int updateWithVersionCheck(@Param("id") {{ id_type }} id,
                              @Param("{{ new_value }}") {{ field_type }} {{ new_value }},
                              @Param("modifiedDate") LocalDateTime modifiedDate,
                              @Param("version") Long version);
}

// Service implementation
@Service
@Transactional
public class {{ service_name }} {
    
    public {{ entity_name }} update{{ entity_name }}({{ entity_name }} entity) {
        try {
            validateRequired(entity, "{{ entity_name | lower }}");
            validateRequired(entity.getId(), "{{ entity_name | lower }} ID");
            
            // Verify entity exists
            {{ entity_name }} existingEntity = repository.findById(entity.getId())
                .orElseThrow(() -> new EntityNotFoundException("{{ entity_name }} not found with ID: " + entity.getId()));
            
            // Business validation
            validateUpdateBusinessRules(entity, existingEntity);
            
            // Set audit fields
            entity.setLastModifiedDate(LocalDateTime.now());
            entity.setLastModifiedBy(getCurrentUser());
            
            {{ entity_name }} updatedEntity = repository.save(entity);
            logger.info("Successfully updated {{ entity_name }} with ID: {}", updatedEntity.getId());
            return updatedEntity;
            
        } catch (OptimisticLockingFailureException e) {
            logger.error("Optimistic locking failure updating {{ entity_name }}: {}", entity.getId(), e);
            throw new DatabaseConcurrencyException("{{ entity_name }} was modified by another transaction", e);
        } catch (DataAccessException e) {
            logger.error("SQL error updating {{ entity_name }}: {}", entity, e);
            throw new DatabaseOperationException("Failed to update {{ entity_name }}", e);
        }
    }
    
    // Bulk update operation
    @Transactional
    public int bulkUpdate{{ field_name | title }}({{ field_type }} {{ new_value }}, {{ condition_type }} {{ condition_value }}) {
        try {
            int updatedCount = repository.updateBy{{ condition_field | title }}({{ new_value }}, LocalDateTime.now(), {{ condition_value }});
            logger.info("Successfully updated {} {{ entity_name }} records", updatedCount);
            return updatedCount;
            
        } catch (DataAccessException e) {
            logger.error("SQL error bulk updating {{ entity_name }} records", e);
            throw new DatabaseOperationException("Failed to bulk update {{ entity_name }} records", e);
        }
    }
}
```

**4. EXEC SQL DELETE Operations:**
```java
// COBOL: EXEC SQL DELETE FROM table WHERE condition END-EXEC
// Java: Convert to JPA delete operation

@Repository
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {
    
    @Modifying
    @Query("DELETE FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }}")
    int deleteBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }});
    
    @Modifying
    @Query("DELETE FROM {{ entity_name }} e WHERE e.{{ field_name }} IN :{{ param_name }}List")
    int deleteByMultiple{{ field_name | title }}(@Param("{{ param_name }}List") List<{{ param_type }}> {{ param_name }}List);
    
    // Soft delete (logical delete)
    @Modifying
    @Query("UPDATE {{ entity_name }} e SET e.deleted = true, e.deletedDate = :deletedDate, e.deletedBy = :deletedBy " +
           "WHERE e.{{ field_name }} = :{{ param_name }}")
    int softDeleteBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }},
                                           @Param("deletedDate") LocalDateTime deletedDate,
                                           @Param("deletedBy") String deletedBy);
}

// Service implementation
@Service
@Transactional
public class {{ service_name }} {
    
    public void delete{{ entity_name }}By{{ field_name | title }}({{ param_type }} {{ param_name }}) {
        try {
            // Verify entity exists before deletion
            if (!repository.existsBy{{ field_name | title }}({{ param_name }})) {
                throw new EntityNotFoundException("No {{ entity_name }} found to delete with {{ field_name }}: " + {{ param_name }});
            }
            
            // Business validation before deletion
            validateDeleteBusinessRules({{ param_name }});
            
            int deletedCount = repository.deleteBy{{ field_name | title }}({{ param_name }});
            logger.info("Successfully deleted {} {{ entity_name }} record(s) with {{ field_name }}: {}", deletedCount, {{ param_name }});
            
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation deleting {{ entity_name }} with {{ field_name }}: {}", {{ param_name }}, e);
            throw new DatabaseConstraintException("Cannot delete {{ entity_name }} due to foreign key constraints", e);
        } catch (DataAccessException e) {
            logger.error("SQL error deleting {{ entity_name }} with {{ field_name }}: {}", {{ param_name }}, e);
            throw new DatabaseOperationException("Failed to delete {{ entity_name }}", e);
        }
    }
}
```

**5. Host Variable Conversion:**
```java
// COBOL: Host variables (:host-var-name)
// Java: Method parameters with @Param annotation

// COBOL Example:
// EXEC SQL SELECT COUNT(*) INTO :WS-COUNT FROM table WHERE field = :WS-FIELD-VALUE END-EXEC

// Java Conversion:
@Query("SELECT COUNT(e) FROM {{ entity_name }} e WHERE e.{{ field_name }} = :fieldValue")
Long countBy{{ field_name | title }}(@Param("fieldValue") {{ param_type }} fieldValue);

// Service method:
public Long getCountBy{{ field_name | title }}({{ param_type }} fieldValue) {
    try {
        return repository.countBy{{ field_name | title }}(fieldValue);
    } catch (DataAccessException e) {
        logger.error("SQL error counting {{ entity_name }} records", e);
        throw new DatabaseOperationException("Failed to count {{ entity_name }} records", e);
    }
}
```

**6. Transaction Isolation Level Conversion:**
```java
// COBOL: WITH UR (Uncommitted Read)
// Java: @Transactional with isolation level

@Transactional(isolation = Isolation.READ_UNCOMMITTED)
public List<{{ entity_name }}> getDirtyReads{{ entity_name }}() {
    // Equivalent to WITH UR in COBOL
    return repository.findAll();
}

// COBOL: WITH CS (Cursor Stability) - Default
@Transactional(isolation = Isolation.READ_COMMITTED)
public List<{{ entity_name }}> getCommittedReads{{ entity_name }}() {
    return repository.findAll();
}

// COBOL: WITH RR (Repeatable Read)
@Transactional(isolation = Isolation.REPEATABLE_READ)
public List<{{ entity_name }}> getRepeatableReads{{ entity_name }}() {
    return repository.findAll();
}
```

**7. Large Dataset Processing Operations:**
```java
// COBOL: Process large datasets sequentially with controlled memory usage
// Java: Use JPA Slice or Page for large result sets

@Repository
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {
    
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }} ORDER BY e.{{ sort_field }}")
    Slice<{{ entity_name }}> findSliceBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }}, Pageable pageable);
}

// Service implementation for cursor-like processing
@Service
@Transactional(readOnly = true)
public class {{ service_name }} {
    
    public void processCursorLike{{ entity_name }}({{ param_type }} {{ param_name }}, int batchSize) {
        Pageable pageable = PageRequest.of(0, batchSize, Sort.by("{{ sort_field }}"));
        Slice<{{ entity_name }}> slice;
        
        do {
            slice = repository.findSliceBy{{ field_name | title }}({{ param_name }}, pageable);
            
            // Process current batch
            slice.getContent().forEach(this::processEntity);
            
            pageable = slice.nextPageable();
        } while (slice.hasNext());
    }
    
    private void processEntity({{ entity_name }} entity) {
        // Process individual entity (sequential record processing)
        logger.debug("Processing {{ entity_name }} with ID: {}", entity.getId());
    }
}
```

**ERROR HANDLING PATTERNS:**

**Transaction Result Validation:**
```java
// COBOL: Validate database operation completion status
// Java: Use try-catch with specific Spring Data exceptions

// Validate successful operation completion
try {
    // Database operation
    result = repository.operation();
    // Success - continue processing
} catch (DataAccessException e) {
    // Handle database errors
}

// Validate data availability
try {
    Optional<Entity> result = repository.findById(id);
    if (result.isEmpty()) {
        // Handle no data available scenario
        logger.info("No data found for ID: {}", id);
        return null; // or throw appropriate exception
    }
} catch (DataAccessException e) {
    // Handle other database errors
}

// Validate operation constraints
try {
    repository.operation();
} catch (DataIntegrityViolationException e) {
    // Handle business rule violations
    throw new DatabaseConstraintException("Business constraint violation", e);
} catch (DataAccessException e) {
    // Handle other database errors
    throw new DatabaseOperationException("Database operation failed", e);
}
```

**REQUIRED IMPORTS:**
```java
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Slice;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.OptimisticLockingFailureException;
import javax.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;
```
