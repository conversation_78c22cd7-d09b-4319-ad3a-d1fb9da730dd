Generate a Spring Boot Service class for COBOL database operations.

**BUSINESS FUNCTION:** {{ business_name }}
**DESCRIPTION:** {{ business_description }}

**FUNCTIONAL SPECIFICATION:**
{{ functional_spec }}

**COBOL DATABASE OPERATIONS:**
{% if database_operations %}
{% for operation in database_operations %}
- **{{ operation.type }}**: {{ operation.description }}
  - Original COBOL: `{{ operation.cobol_code }}`
  - Business Logic: {{ operation.business_logic }}
{% endfor %}
{% endif %}

**EXACT METHOD SIGNATURE REQUIRED:**
{% if input_parameters %}
Input Parameters:
{% for param in input_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
{% endfor %}
{% endif %}

{% if output_parameters %}
Output Parameters:
{% for param in output_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
{% endfor %}
{% endif %}

**AVAILABLE BUSINESS DATA CLASSES:**

{% if has_aggregate_wrappers %}
**AGGREGATE WRAPPER CLASSES (COMPLETE BUSINESS ENTITIES - USE THESE PRIMARILY):**
{% for data_class in aggregate_wrapper_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - **AGGREGATE WRAPPER** for COBOL Structure: {{ data_class.cobol_structure_name }}
  - **BUSINESS ENTITY** - Use for database operations
{% endfor %}
{% endif %}

{% if regular_data_classes %}
**REGULAR DATA CLASSES (NON-CHUNKED STRUCTURES):**
{% for data_class in regular_data_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - COBOL Structure: {{ data_class.cobol_structure_name }}
  - Complete business entity (not chunked)
  - Fields: {{ data_class.field_count }}
{% endfor %}
{% endif %}

{% if stored_mappings and stored_mappings.service_dependencies %}
**SERVICE DEPENDENCIES (INJECT THESE BEANS):**
{% for cobol_call, service_info in stored_mappings.service_dependencies.items() %}
- @Autowired {{ service_info.java_service_name }} {{ service_info.field_name }}
  - Method: {{ service_info.method_signature }}
  - Purpose: {{ service_info.business_purpose }}
  - For COBOL: {{ cobol_call }}
{% endfor %}
{% endif %}

**VARIABLE MAPPINGS (USE EXACT NAMES):**
{% if variable_java_mappings %}
{% for cobol_name, java_info in variable_java_mappings.items() %}
- {{ cobol_name }} → {{ java_info.java_name }} ({{ java_info.java_data_type }})
{% endfor %}
{% endif %}

**REQUIREMENTS:**
1. Generate complete Spring Boot Service class with @Service annotation
2. Convert all COBOL database operations to Spring Data JPA calls
3. Implement comprehensive error handling with proper exception mapping
4. Add transaction management with appropriate isolation levels
5. Include business validation and logging
6. Use exact method signatures from parameters above

**DATABASE OPERATION CONVERSION:**

**Data Access Operations:**
- Unique record retrieval → JPA repository findBy methods with LIMIT 1
- Sequential record processing → JPA repository findAll with Pageable
- Exclusive record access → JPA repository findBy with @Lock(LockModeType.PESSIMISTIC_WRITE)
- Record creation → JPA repository save() method
- Record removal → JPA repository delete() methods
- Record updates → JPA repository save() for updates

**Database Query Operations:**
- Data selection → JPA @Query annotations or repository methods
- Data modification operations → JPA repository operations
- Input parameters → method parameters with @Param
- Large dataset processing → JPA Slice or Page for large result sets

**Operation Status Validation:**
- Successful operation completion → try-catch blocks with successful execution
- Record not found conditions → EntityNotFoundException
- Successful transaction completion → successful execution
- No data available conditions → Optional.empty() or EntityNotFoundException
- Duplicate data violations → DatabaseConstraintException
- etc. Process other validation scenarios accordingly

**IMPLEMENTATION ESSENTIALS:**
- **MUST use existing Java data classes** listed above
- Generate complete Spring Boot service class with @Service annotation
- Use @Slf4j for logging and @RequiredArgsConstructor for dependency injection
- Package: com.generated.cobol.service
- **MANDATORY JavaDoc:** Include "Generated from COBOL chunk: {{ chunk_name }}"
- **MUST inject service dependencies** as final fields
- Use exact input_parameters and output_parameters specified
- **MUST USE Variable Java Mappings** from above section
- Convert functional specification to equivalent Java implementation
- **NO TODO comments** - generate complete working code
- Add comprehensive logging and error handling
- Use @Transactional annotations with appropriate isolation levels
- Implement proper exception handling for database operations

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "service_info": {
    "service_name": "generated class name",
    "package": "com.generated.cobol.service",
    "business_purpose": "what this service does"
  },
  "method_mappings": {
    "{{ chunk_name }}": {
      "java_method_name": "generated method name",
      "method_signature": "complete signature with types",
      "business_purpose": "method purpose"
    }
  },
  "parameter_mappings": {
    "COBOL-PARAM": {
      "java_name": "generated name",
      "java_type": "type used",
      "parameter_type": "input/output",
      "business_name": "business description"
    }
  },
  "variable_mappings": {
    "COBOL-VAR": {
      "java_variable_name": "generated name",
      "business_name": "business purpose",
      "scope": "method/class"
    }
  },
  "database_operations": {
    "COBOL-OPERATION": {
      "java_method_name": "generatedMethodName",
      "operation_type": "CREATE/READ/UPDATE/DELETE",
      "jpa_method": "repository.save() / repository.findBy() / etc.",
      "business_purpose": "what this operation does"
    }
  }
}
```
