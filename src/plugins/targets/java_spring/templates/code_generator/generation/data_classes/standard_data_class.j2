Generate a Java data class for COBOL structure.

**COBOL STRUCTURE:** {{ structure.name }}
**BUSINESS PURPOSE:** Standard data structure (no file operations)

{% if has_ims_segments %}
**IMS SEGMENT CONTEXT:**
This structure is associated with the following IMS database segments:
{% for segment in ims_segments %}
- **{{ segment }}**: {{ segment_business_mappings.get(segment, 'Unknown business context') }}
{% endfor %}
{% endif %}

**FIELD DEFINITIONS:**
{% for field in structure.fields %}
Level {{ '%02d' | format(field.level) }}: {{ field.name }}
  - Raw: {{ field.raw_definition }}
  {% if field.name in business_mappings %}
  - Business Name: {{ business_mappings[field.name].business_name }}
  - Description: {{ business_mappings[field.name].description }}
  {% endif %}
{% endfor %}

**NAMING PATTERNS (MAINTAIN CONSISTENCY):**
{% for pattern in naming_patterns %}
- {{ pattern.cobol_example }} → {{ pattern.java_example }}
{% endfor %}

**EXISTING FIELD MAPPINGS:**
{% for cobol_name, java_name in existing_mappings.items() %}
- {{ cobol_name }} → {{ java_name }}
{% endfor %}

**ENTITY CLASSIFICATION:**
{% if has_ims_segments %}
**IMPORTANT:** This structure is associated with IMS database segments and should be generated as a JPA Entity class with @Entity annotation for database persistence.
{% else %}
**IMPORTANT:** This structure has no database operations detected and should be generated as a regular data class (not an entity).
{% endif %}

**REQUIREMENTS:**
1. Generate meaningful class name from business context
2. {% if has_ims_segments %}JPA Entity annotations (@Entity, @Table, @Id, @Column) plus Lombok annotations{% else %}Standard Lombok annotations (@Data, @NoArgsConstructor, @AllArgsConstructor, @Builder){% endif %}
3. NO string constructor needed (not used in file operations)
4. Generate field names from business names (if business name is not available - give the field a name that represents its purpose, do not just camelcase it)
5. **MANDATORY JavaDoc with COBOL traceability:**
   - Class-level JavaDoc must include: "Generated from COBOL structure: {{ structure.name }}"
   - Each field JavaDoc must include: "COBOL Field: [original-cobol-field-name]"
   - Include business purpose and field descriptions

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "class_info": {
    "cobol_structure_name": "{{ structure.name }}",
    "java_class_name": "generated name",
    "package": "{{ 'com.generated.cobol.entities' if has_ims_segments else 'com.generated.cobol.model' }}",
    "business_purpose": "what this represents",
    "has_string_constructor": false,
    "is_entity": {{ 'true' if has_ims_segments else 'false' }}
  },
  "field_mappings": {
    "COBOL-FIELD": {
      "java_field_name": "generated name",
      "java_type": "type",
      "business_name": "business description"
    }
  }
}
```
